<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Notion Productivity Tool - <PERSON></title>
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&family=Noto+Sans:wght@400;500;700;900&display=swap" rel="stylesheet" />
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  <link rel="icon" href="images/Logo.png" type="image/x-icon" />
  <style type="text/tailwindcss">
    :root {
      --primary-color: #0c7ff2;
      --background-color: #111418;
      --card-color: #1a2026;
      --text-primary: #ffffff;
      --text-secondary: #9cabba;
      --border-color: #283039;
    }
    body {
      font-family: "Space Grotesk", "Noto Sans", sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
    }
    .primary-button { @apply inline-flex items-center justify-center rounded-md px-6 py-2 font-semibold shadow-md transition bg-gradient-to-r from-orange-500 to-yellow-500 text-white hover:brightness-110; }
  </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)] min-h-screen">
  <a id="top"></a>
  <div class="max-w-3xl mx-auto py-10 px-4">
    <div class="bg-[var(--card-color)] rounded-xl shadow-lg p-6 flex flex-col gap-4">
      <a href="projects.html" class="primary-button mb-2">&larr; Back to Projects</a>
      <h1 class="text-4xl font-bold mb-1">Notion Productivity Tool</h1>
      <div>
        <h2 class="text-lg font-semibold mb-1">Tech Stack</h2>
        <ul class="flex flex-wrap gap-2 mb-2">
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">n8n</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">Notion API</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">Automation</li>
        </ul>
      </div>
      <div>
        <h2 class="text-lg font-semibold mb-1">Demo Video</h2>
        <div class="aspect-video w-full rounded-lg overflow-hidden bg-black mb-2">
          <video controls poster="images/project-hero.png" class="w-full h-full">
            <source src="videos/notion-demo.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        I built an automation system that connects with Notion to review your previous day's to-do list and assess your productivity. It checks task completion, analyzes what was left undone, and then asks three critical questions to help plan the next day more effectively.
      </p>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        <strong>What I did:</strong><br />
        – Used n8n to connect with Notion's API and fetch task databases<br />
        – Built conditional logic to review completion status of tasks<br />
        – Sent automated questions via email or messaging tool to encourage daily reflection and planning<br />
        – Designed the flow to run every evening for proactive productivity review
      </p>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        This workflow shows my ability to combine productivity principles with automation to create tools that support daily improvement and accountability.
      </p>
      <a href="#top" class="primary-button mt-4">Back to Top &uarr;</a>
    </div>
  </div>
</body>
</html>
