<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title><PERSON> - MERN Developer &amp; AI Expert</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&amp;family=Noto+Sans:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #0c7ff2;
        --background-color: #111418;
        --card-color: #1a2026;
        --text-primary: #ffffff;
        --text-secondary: #9cabba;
        --border-color: #283039;
      }
      body {
        font-family: "Space Grotesk", "Noto Sans", sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
      }
      .nav-link {
        @apply text-white text-sm font-medium leading-normal transition-colors duration-300 hover:text-[var(--primary-color)];
      }
      .primary-button {
        @apply flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[var(--primary-color)] text-white text-sm font-bold leading-normal tracking-[0.015em] transition-transform duration-300 hover:scale-105;
      }
       .secondary-button {
        @apply flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-8 px-4 bg-[var(--border-color)] text-white text-sm font-medium leading-normal w-fit transition-colors duration-300 hover:bg-[var(--primary-color)];
      }
    </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
<div class="relative flex size-full min-h-screen flex-col overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<header class="sticky top-0 z-50 flex items-center justify-between whitespace-nowrap border-b border-solid border-[var(--border-color)] bg-[var(--background-color)]/80 px-10 py-4 backdrop-blur-sm">
<div class="flex items-center gap-4">
<h2 class="text-xl font-bold tracking-tight">Thomas van Rossum</h2>
</div>
<nav class="hidden md:flex items-center gap-9">
<a class="nav-link" href="#home">Home</a>
<a class="nav-link" href="projects.html">Projects</a>
<a class="nav-link" href="#about">About</a>
<a class="nav-link" href="#contact">Contact</a>
</nav>
<a class="primary-button hidden md:flex bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition" href="#">Full Freelance Portfolio</a>
</header>
<main class="px-4 md:px-20 lg:px-40 flex flex-1 justify-center py-10">
<div class="layout-content-container flex flex-col max-w-5xl flex-1 gap-16">
<section class="grid grid-cols-1 lg:grid-cols-2 items-center gap-12 pt-10" id="home">
<div class="flex flex-col gap-6 text-left">
<h1 class="text-5xl font-black tracking-tighter sm:text-6xl md:text-7xl">
                    Hi, I'm Thomas van Rossum
                  </h1>
<p class="text-lg text-[var(--text-secondary)]">
                    A MERN stack developer and AI automation expert, I specialize in creating efficient and scalable web applications and automating processes with AI. Let's
                    build something amazing together.
                  </p>
<div class="flex gap-4">
<a class="primary-button h-12 px-6 text-base bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition" href="projects.html">
                      View Projects
                    </a>
<a class="secondary-button h-12 px-6 text-base bg-transparent border-2 border-[var(--border-color)] hover:bg-[var(--border-color)] hover:text-white" href="#contact">
                      Get in Touch
                    </a>
</div>
</div>
<div class="relative group w-96 h-96">
    <!-- Glow stays perfectly aligned -->
    <div class="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full blur-xl opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-tilt"></div>
    
    <!-- Circle border & overflow -->
    <div class="relative w-full h-full rounded-full overflow-hidden shadow-lg">
      <!-- Scaled (zoomed) image inside -->
      <img alt="Thomas van Rossum's profile picture"
           class="w-full h-full object-cover scale-125"
           src="images/LogoNew.png" />
    </div>
</div>
</section>
<section id="projects">
<h2 class="text-3xl font-bold tracking-tight mb-8">Featured Skills</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
<div class="bg-[var(--card-color)] rounded-xl overflow-hidden group transform transition duration-500 hover:scale-130">
<img alt="Thomas van Rossum" class="w-full h-56 object-cover" src="images/LogoNew.png"/>
<div class="p-6">
<h3 class="text-xl font-bold mb-2">AI Automation</h3>
<p class="text-[var(--text-secondary)] mb-4">A full-stack web application built with React, Node.js, Express, and MongoDB. It features user authentication, dynamic content management, and a responsive design.</p>
<a class="secondary-button" href="projects.html">View Projects</a>
</div>
</div>
<div class="bg-[var(--card-color)] rounded-xl overflow-hidden group transform transition duration-500 hover:scale-105">
<img alt="Project Beta image" class="w-full h-56 object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBbXq5EiQmJ_vY-PxXFpfw2WKQuiKXH-YIVEl_Gj5V7_ztiAbwsAh1-5RpwEDYVf3FemmkdmXGO21jhqmazcQr2xOsKZj42EGCe76Wqye_3xYaWNVlESwP6qnlnOAVT4HHyTxL9YMa5MJR6K4Kng_jDC_5gXCo4I3HCXkTQybzLFln1ioQbR5mBwT6CKGZ-sYw_22JTlR-W9RTnvoRXSZ6EYeR0v0kAAYXoiwARcqGRIuhsZ70bO5kZxHRl9bMqcdwe6podkgpAq1U"/>
<div class="p-6">
<h3 class="text-xl font-bold mb-2">MERN-Stack Development</h3>
<p class="text-[var(--text-secondary)] mb-4">An AI-powered automation tool that streamlines data processing and reporting. Built with Python and integrated with various APIs for seamless workflow automation.</p>
<a class="secondary-button" href="projects.html">View Projects</a>
</div>
</div>
</div>
</section>
<section class="grid grid-cols-1 md:grid-cols-3 items-center gap-12" id="about">
<div class="md:col-span-1">
<img alt="A more professional picture of Thomas van Rossum" class="rounded-full w-48 h-48 md:w-full md:h-auto object-cover mx-auto" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCvp80rJuKn8N3ik0W6UhZihbsRool-gHU8y1VRuAYxFGZ60e2IYdRvkYFeSk77hHnQ9-1zsdZxOY7fhqKO3KGEs-yRWTQWcfvkhSJcckwgoDxQQEoSoC2uJCWBSCJqDUqmOBptYe5WyrXmaQIKQ92fEzV0GybYXsEF27d8tThMuFwj6LdrrF6KL0SNMIDxJAwcMNzc4fWjLXIdd7oBwzjDjb8RIAPPE4ZX-PiUzp33S1DvfKoz1UyAakR2imr-cPFDH8wHPf1EOvs"/>
</div>
<div class="md:col-span-2 flex flex-col gap-4">
<h2 class="text-3xl font-bold tracking-tight">About Me</h2>
<p class="text-[var(--text-secondary)]">
                    I'm a passionate developer with a focus on creating robust and scalable web applications using the MERN stack. My expertise extends to AI automation, where I
                    leverage cutting-edge technologies to optimize workflows and enhance productivity. With a strong foundation in both front-end and back-end development, I
                    bring a holistic approach to every project, ensuring seamless integration and exceptional performance. Let's collaborate to bring your ideas to life.
                  </p>
</div>
</section>
<section id="skills">
<h2 class="text-3xl font-bold tracking-tight mb-8 text-center">Core Technologies</h2>
<div class="flex gap-4 flex-wrap justify-center">
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">React</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">Node.js</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">Express</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">MongoDB</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">JavaScript</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">Python</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">AI Automation</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">REST APIs</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">GraphQL</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5">
<p class="text-sm font-medium">Docker</p>
</div>
</div>
</section>
<section class="text-center bg-[var(--card-color)] rounded-xl p-10 my-10" id="contact">
<div class="flex flex-col items-center gap-6">
<h2 class="text-4xl font-black tracking-tighter max-w-2xl">
                  Have a project in mind? Let's build something great together.
                </h2>
<a class="primary-button h-12 px-6 text-base" href="mailto:<EMAIL>">
                  Get in Touch
                </a>
</div>
</section>
</div>
</main>
<footer class="border-t border-solid border-[var(--border-color)]">
<div class="container mx-auto max-w-5xl px-5 py-8">
<div class="flex flex-col md:flex-row items-center justify-between gap-6">
<div class="flex flex-wrap items-center justify-center gap-6">
<a class="nav-link" href="#home">Home</a>
<a class="nav-link" href="projects.html">Projects</a>
<a class="nav-link" href="#about">About</a>
<a class="nav-link" href="#contact">Contact</a>
</div>
<div class="flex justify-center gap-4">
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)]" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path>
</svg>
</a>
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)]" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path>
</svg>
</a>
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)]" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"></path>
</svg>
</a>
</div>
<p class="text-[var(--text-secondary)] text-sm">© 2024 Thomas van Rossum. All rights reserved.</p>
</div>
</div>
</footer>
</div>
</div>

</body></html>