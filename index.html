<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title><PERSON> - MERN Developer &amp; AI Expert</title>
<link href="images/Logo.png" rel="icon" type="image/x-icon"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&amp;family=Noto+Sans:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #0c7ff2;
        --background-color: #111418;
        --card-color: #1a2026;
        --text-primary: #ffffff;
        --text-secondary: #9cabba;
        --border-color: #283039;
      }
      html, body {
        font-family: "Space Grotesk", "Noto Sans", sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
        overflow-x: hidden;
        margin: 0;
        padding: 0;
      }

      /* Subtle background pattern */
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
          radial-gradient(circle at 20% 80%, rgba(12, 127, 242, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 165, 0, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.02) 0%, transparent 50%);
        pointer-events: none;
        z-index: -2;
      }

      /* Animated background shapes */
      body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.05), transparent),
          radial-gradient(2px 2px at 40px 70px, rgba(12, 127, 242, 0.08), transparent),
          radial-gradient(1px 1px at 90px 40px, rgba(255, 165, 0, 0.06), transparent),
          radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.04), transparent),
          radial-gradient(2px 2px at 160px 30px, rgba(255, 215, 0, 0.05), transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: backgroundMove 20s linear infinite;
        pointer-events: none;
        z-index: -1;
      }

      @keyframes backgroundMove {
        0% { transform: translateY(0px); }
        100% { transform: translateY(-100px); }
      }
      .nav-link {
        @apply text-white text-sm font-medium leading-normal transition-colors duration-300 hover:text-[var(--primary-color)];
      }
      .primary-button {
        @apply flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[var(--primary-color)] text-white text-sm font-bold leading-normal tracking-[0.015em] transition-transform duration-300 hover:scale-105;
      }
       .secondary-button {
        @apply flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-8 px-4 bg-[var(--border-color)] text-white text-sm font-medium leading-normal w-fit transition-colors duration-300 hover:bg-[var(--primary-color)];
      }
      /* Added subtle tilt animation for the hero glow */
      @keyframes tilt {
        0%, 100% { transform: rotate(-1deg) scale(1); }
        50% { transform: rotate(1deg) scale(1.02); }
      }
      .animate-tilt { animation: tilt 10s ease-in-out infinite; }
      /* Enhanced Reveal/Load base styles */
      .pre-animate { opacity: 0; transform: translateY(20px) scale(0.95); }
      .reveal-in { opacity: 1; transform: translateY(0) scale(1); transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .load-hidden { opacity: 0; transform: translateY(15px) scale(0.96); transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .load-reveal { opacity: 1; transform: translateY(0) scale(1); transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94); }

      /* Enhanced floating animations */
      @keyframes floatIn {
        0% { opacity: 0; transform: translateY(30px) translateX(-10px) scale(0.9); }
        60% { opacity: 1; transform: translateY(-5px) translateX(2px) scale(1.02); }
        100% { opacity: 1; transform: translateY(0) translateX(0) scale(1); }
      }

      @keyframes glideInLeft {
        0% { opacity: 0; transform: translateX(-50px) translateY(20px) scale(0.9); }
        60% { opacity: 1; transform: translateX(5px) translateY(-3px) scale(1.01); }
        100% { opacity: 1; transform: translateX(0) translateY(0) scale(1); }
      }

      @keyframes glideInRight {
        0% { opacity: 0; transform: translateX(50px) translateY(20px) scale(0.9); }
        60% { opacity: 1; transform: translateX(-5px) translateY(-3px) scale(1.01); }
        100% { opacity: 1; transform: translateX(0) translateY(0) scale(1); }
      }

      @keyframes glideInUp {
        0% { opacity: 0; transform: translateY(40px) scale(0.95); }
        60% { opacity: 1; transform: translateY(-8px) scale(1.02); }
        100% { opacity: 1; transform: translateY(0) scale(1); }
      }

      /* Premium hero animations */
      @keyframes heroPop {
        0% { opacity: 0; transform: translateY(25px) scale(0.95); }
        50% { opacity: 0.8; transform: translateY(-8px) scale(1.02); }
        70% { opacity: 1; transform: translateY(2px) scale(1.01); }
        100% { opacity: 1; transform: translateY(0) scale(1); }
      }

      @keyframes glowReveal {
        0% { opacity: 0; transform: scale(0.7); filter: blur(30px); }
        50% { opacity: 0.6; transform: scale(1.1); filter: blur(18px); }
        100% { opacity: 0.85; transform: scale(1); filter: blur(12px); }
      }

      .animate-hero-pop { animation: heroPop 1100ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }
      .animate-glow { animation: glowReveal 1400ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }
      .animate-float-in { animation: floatIn 1000ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }
      .animate-glide-left { animation: glideInLeft 900ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }
      .animate-glide-right { animation: glideInRight 900ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }
      .animate-glide-up { animation: glideInUp 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) both; }

      /* Enhanced text shine on heading */
      .text-shine {
        background-image: linear-gradient(90deg, rgba(255,255,255,0.8) 0%, #ffffff 25%, rgba(255,255,255,0.8) 50%);
        -webkit-background-clip: text; background-clip: text; color: transparent;
        background-size: 200% 100%;
        animation: shine 1500ms ease-out 500ms 1 both;
      }
      @keyframes shine {
        from { background-position: -200% 50%; }
        to { background-position: 200% 50%; }
      }

      /* Continuous floating animation for elements */
      @keyframes continuousFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-3px) rotate(0.5deg); }
        50% { transform: translateY(-6px) rotate(0deg); }
        75% { transform: translateY(-3px) rotate(-0.5deg); }
      }
      .float-continuous { animation: continuousFloat 4s ease-in-out infinite; }

      /* Floating geometric shapes */
      .floating-shapes {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
      }

      .shape {
        position: absolute;
        opacity: 0.03;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
      }

      .shape-circle {
        border-radius: 50%;
        background: linear-gradient(45deg, var(--primary-color), rgba(255, 165, 0, 0.8));
      }

      .shape-square {
        background: linear-gradient(45deg, rgba(255, 215, 0, 0.6), var(--primary-color));
        transform: rotate(45deg);
      }

      .shape-triangle {
        width: 0;
        height: 0;
        background: transparent;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-bottom: 26px solid rgba(12, 127, 242, 0.4);
      }

      @keyframes floatUp {
        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 0.03; }
        90% { opacity: 0.03; }
        100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
      }

      @keyframes floatDiagonal {
        0% { transform: translate(-100px, 100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 0.02; }
        90% { opacity: 0.02; }
        100% { transform: translate(100vw, -100px) rotate(180deg); opacity: 0; }
      }

      @keyframes floatSide {
        0% { transform: translateX(-100px) rotate(0deg); opacity: 0; }
        10% { opacity: 0.025; }
        90% { opacity: 0.025; }
        100% { transform: translateX(100vw) rotate(90deg); opacity: 0; }
      }
    </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
<!-- Floating geometric shapes for subtle background animation -->
<div class="floating-shapes">
  <!-- Circles -->
  <div class="shape shape-circle" style="width: 20px; height: 20px; left: 10%; animation: floatUp 25s infinite; animation-delay: 0s;"></div>
  <div class="shape shape-circle" style="width: 15px; height: 15px; left: 80%; animation: floatUp 30s infinite; animation-delay: 5s;"></div>
  <div class="shape shape-circle" style="width: 25px; height: 25px; left: 60%; animation: floatUp 35s infinite; animation-delay: 10s;"></div>

  <!-- Squares -->
  <div class="shape shape-square" style="width: 18px; height: 18px; left: 30%; animation: floatDiagonal 40s infinite; animation-delay: 2s;"></div>
  <div class="shape shape-square" style="width: 12px; height: 12px; left: 70%; animation: floatDiagonal 28s infinite; animation-delay: 8s;"></div>

  <!-- Triangles -->
  <div class="shape shape-triangle" style="left: 20%; top: 60%; animation: floatSide 32s infinite; animation-delay: 4s;"></div>
  <div class="shape shape-triangle" style="left: 90%; top: 30%; animation: floatSide 38s infinite; animation-delay: 12s;"></div>

  <!-- Additional subtle shapes -->
  <div class="shape shape-circle" style="width: 8px; height: 8px; left: 45%; animation: floatUp 42s infinite; animation-delay: 15s;"></div>
  <div class="shape shape-square" style="width: 10px; height: 10px; left: 85%; animation: floatDiagonal 36s infinite; animation-delay: 18s;"></div>
</div>

<div class="relative flex w-full min-h-screen flex-col">
<div class="layout-container flex w-full grow flex-col">
<header class="sticky top-0 z-50 flex items-center justify-between whitespace-nowrap border-b border-solid border-[var(--border-color)] bg-[var(--background-color)]/80 px-10 py-4 backdrop-blur-sm animate-glide-up" style="animation-delay: 0ms;">
<div class="flex items-center gap-4 animate-float-in" style="animation-delay: 100ms;">
<h2 class="text-xl font-bold tracking-tight">Thomas van Rossum</h2>
</div>
<nav class="hidden md:flex items-center gap-9 animate-float-in" style="animation-delay: 200ms;">
<a class="nav-link" href="#home">Home</a>
<a class="nav-link" href="projects.html">Projects</a>
<a class="nav-link" href="#about">About</a>
<a class="nav-link" href="blog.html">Blog</a>
</nav>
<a class="primary-button hidden md:flex bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition animate-float-in" style="animation-delay: 300ms;" href="projects.html">Full Freelance Portfolio</a>
</header>
<main class="px-4 md:px-20 lg:px-40 flex flex-1 justify-center py-10">
<div class="layout-content-container flex flex-col max-w-5xl flex-1 gap-16">
<section class="grid grid-cols-1 lg:grid-cols-2 items-center gap-12 pt-10" id="home">
<div class="flex flex-col gap-6 text-left">
<h1 class="text-5xl font-black tracking-tighter sm:text-6xl md:text-7xl animate-hero-pop text-shine" style="animation-delay:0ms;">
                    Hi, I'm Thomas van Rossum
                  </h1>
<p class="text-lg text-[var(--text-secondary)] animate-hero-pop" style="animation-delay:120ms;">
                    A MERN stack developer and AI automation expert, I specialize in creating efficient and scalable web applications and automating processes with AI. Let's
                    build something amazing together.
                  </p>
<div class="flex gap-4 animate-hero-pop" style="animation-delay:220ms;">
<a class="primary-button h-12 px-6 text-base bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition" href="projects.html">
                      View Projects
                    </a>
<a class="secondary-button h-12 px-6 text-base bg-transparent border-2 border-[var(--border-color)] hover:bg-[var(--border-color)] hover:text-white" href="#about">
                      Learn More
                    </a>
</div>
</div>
<div class="relative group w-96 h-96 animate-hero-pop" style="animation-delay:180ms;" id="hero-visual">
    <!-- Glow stays perfectly aligned -->
    <div class="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full blur-xl opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-tilt animate-glow" style="animation-delay:280ms;" id="hero-glow"></div>
    
    <!-- Circle border & overflow -->
    <div class="relative w-full h-full rounded-full overflow-hidden shadow-lg" id="hero-image">
      <!-- Scaled (zoomed) image inside -->
      <img alt="Thomas van Rossum's profile picture"
           class="w-full h-full object-cover scale-125"
           src="images/LogoNew.png" />
    </div>
</div>
</section>
<section id="projects" class="animate-glide-up" style="animation-delay: 400ms;">
<h2 class="text-3xl font-bold tracking-tight mb-8 animate-float-in" style="animation-delay: 500ms;">Featured Skills</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
<div data-tilt class="bg-[var(--card-color)] rounded-xl overflow-hidden group transform transition duration-500 hover:scale-105 animate-glide-left float-continuous" style="animation-delay: 600ms;">
<img alt="Thomas van Rossum" class="w-full h-56 object-cover" src="images/LogoNew.png"/>
<div class="p-6">
<h3 class="text-xl font-bold mb-2">AI Automation</h3>
<p class="text-[var(--text-secondary)] mb-4">A full-stack web application built with React, Node.js, Express, and MongoDB. It features user authentication, dynamic content management, and a responsive design.</p>
<a class="secondary-button" href="projects.html">View Projects</a>
</div>
</div>
<div data-tilt class="bg-[var(--card-color)] rounded-xl overflow-hidden group transform transition duration-500 hover:scale-105 animate-glide-right float-continuous" style="animation-delay: 750ms;">
<img alt="Project Beta image" class="w-full h-56 object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBbXq5EiQmJ_vY-PxXFpfw2WKQuiKXH-YIVEl_Gj5V7_ztiAbwsAh1-5RpwEDYVf3FemmkdmXGO21jhqmazcQr2xOsKZj42EGCe76Wqye_3xYaWNVlESwP6qnlnOAVT4HHyTxL9YMa5MJR6K4Kng_jDC_5gXCo4I3HCXkTQybzLFln1ioQbR5mBwT6CKGZ-sYw_22JTlR-W9RTnvoRXSZ6EYeR0v0kAAYXoiwARcqGRIuhsZ70bO5kZxHRl9bMqcdwe6podkgpAq1U"/>
<div class="p-6">
<h3 class="text-xl font-bold mb-2">MERN-Stack Development</h3>
<p class="text-[var(--text-secondary)] mb-4">An AI-powered automation tool that streamlines data processing and reporting. Built with Python and integrated with various APIs for seamless workflow automation.</p>
<a class="secondary-button" href="projects.html">View Projects</a>
</div>
</div>
</div>
</section>
<section class="grid grid-cols-1 md:grid-cols-3 items-center gap-12 animate-glide-up" id="about" style="animation-delay: 900ms;">
<div class="md:col-span-1 animate-glide-left float-continuous" style="animation-delay: 1000ms;">
<img alt="A more professional picture of Thomas van Rossum" class="rounded-full w-48 h-48 md:w-full md:h-auto object-cover mx-auto" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCvp80rJuKn8N3ik0W6UhZihbsRool-gHU8y1VRuAYxFGZ60e2IYdRvkYFeSk77hHnQ9-1zsdZxOY7fhqKO3KGEs-yRWTQWcfvkhSJcckwgoDxQQEoSoC2uJCWBSCJqDUqmOBptYe5WyrXmaQIKQ92fEzV0GybYXsEF27d8tThMuFwj6LdrrF6KL0SNMIDxJAwcMNzc4fWjLXIdd7oBwzjDjb8RIAPPE4ZX-PiUzp33S1DvfKoz1UyAakR2imr-cPFDH8wHPf1EOvs"/>
</div>
<div class="md:col-span-2 flex flex-col gap-4 animate-glide-right" style="animation-delay: 1100ms;">
<h2 class="text-3xl font-bold tracking-tight animate-float-in" style="animation-delay: 1200ms;">About Me</h2>
<p class="text-[var(--text-secondary)] animate-float-in" style="animation-delay: 1300ms;">
                    I'm a passionate developer with a focus on creating robust and scalable web applications using the MERN stack. My expertise extends to AI automation, where I
                    leverage cutting-edge technologies to optimize workflows and enhance productivity. With a strong foundation in both front-end and back-end development, I
                    bring a holistic approach to every project, ensuring seamless integration and exceptional performance. Let's collaborate to bring your ideas to life.
                  </p>
</div>
</section>
<section id="skills" class="animate-glide-up" style="animation-delay: 1400ms;">
<h2 class="text-3xl font-bold tracking-tight mb-8 text-center animate-float-in" style="animation-delay: 1500ms;">Core Technologies</h2>
<div class="flex gap-4 flex-wrap justify-center">
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1600ms;">
<p class="text-sm font-medium">React</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1650ms;">
<p class="text-sm font-medium">Node.js</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1700ms;">
<p class="text-sm font-medium">Express</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1750ms;">
<p class="text-sm font-medium">MongoDB</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1800ms;">
<p class="text-sm font-medium">JavaScript</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1850ms;">
<p class="text-sm font-medium">Python</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1900ms;">
<p class="text-sm font-medium">AI Automation</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 1950ms;">
<p class="text-sm font-medium">REST APIs</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 2000ms;">
<p class="text-sm font-medium">GraphQL</p>
</div>
<div class="flex h-10 items-center justify-center gap-x-2 rounded-lg bg-[var(--card-color)] px-5 animate-float-in float-continuous" style="animation-delay: 2050ms;">
<p class="text-sm font-medium">Docker</p>
</div>
</div>
</section>
</div>
</main>
<footer class="border-t border-solid border-[var(--border-color)] animate-glide-up" style="animation-delay: 2100ms;">
<div class="container mx-auto max-w-5xl px-5 py-8">
<div class="flex flex-col md:flex-row items-center justify-between gap-6">
<div class="flex flex-wrap items-center justify-center gap-6 animate-glide-left" style="animation-delay: 2200ms;">
<a class="nav-link" href="#home">Home</a>
<a class="nav-link" href="projects.html">Projects</a>
<a class="nav-link" href="#about">About</a>
<a class="nav-link" href="blog.html">Blog</a>
</div>
<div class="flex justify-center gap-4 animate-float-in" style="animation-delay: 2300ms;">
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)] float-continuous" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path>
</svg>
</a>
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)] float-continuous" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path>
</svg>
</a>
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)] float-continuous" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"></path>
</svg>
</a>
<a class="text-[var(--text-secondary)] transition-colors duration-300 hover:text-[var(--primary-color)] float-continuous" href="#">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"></path>
</svg>
</a>
</div>
<p class="text-[var(--text-secondary)] text-sm animate-glide-right" style="animation-delay: 2400ms;">© 2024 Thomas van Rossum. All rights reserved.</p>
</div>
</div>
</footer>
</div>
</div>

<script>
  // Reveal-on-scroll animations
  (function() {
    const selectors = [
      '.layout-content-container > section:not(#home)',
      '.card',
      '#skills .flex > div',
      'footer .container > div',
    ];
    const targets = document.querySelectorAll(selectors.join(','));
    targets.forEach(el => el.classList.add('pre-animate'));

    const io = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('reveal-in');
          io.unobserve(entry.target);
        }
      });
    }, { threshold: 0.15 });

    targets.forEach(el => io.observe(el));
  })();

  // Page-load stagger for non-hero [data-load]
  (function() {
    function run() {
      const loadEls = document.querySelectorAll('[data-load]');
      loadEls.forEach(el => el.classList.add('load-hidden'));
      loadEls.forEach(el => {
        const delay = parseInt(el.getAttribute('data-load') || '0', 10);
        setTimeout(() => el.classList.add('load-reveal'), delay);
      });
    }
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', run);
    } else {
      run();
    }
  })();

  // Subtle parallax for hero visual
  (function() {
    const hero = document.getElementById('hero-visual');
    const glow = document.getElementById('hero-glow');
    const image = document.getElementById('hero-image');
    if (!hero || !glow || !image) return;

    hero.addEventListener('mousemove', (e) => {
      const r = hero.getBoundingClientRect();
      const relX = (e.clientX - r.left) / r.width - 0.5; // -0.5..0.5
      const relY = (e.clientY - r.top) / r.height - 0.5;
      const glowOffset = 12; // px
      const imgOffset = 6;   // px
      glow.style.transform = `translate(${(-relX * glowOffset).toFixed(1)}px, ${(-relY * glowOffset).toFixed(1)}px) scale(1)`;
      image.style.transform = `translate(${(relX * imgOffset).toFixed(1)}px, ${(relY * imgOffset).toFixed(1)}px)`;
    });
    hero.addEventListener('mouseleave', () => {
      glow.style.transform = 'translate(0px, 0px) scale(1)';
      image.style.transform = 'translate(0px, 0px)';
    });
  })();

  // Lightweight tilt on hover for marked elements
  (function() {
    const tiltEls = document.querySelectorAll('[data-tilt]');
    const dampen = 20;
    tiltEls.forEach(el => {
      el.style.transformStyle = 'preserve-3d';
      el.addEventListener('mousemove', (e) => {
        const r = el.getBoundingClientRect();
        const cx = r.left + r.width / 2;
        const cy = r.top + r.height / 2;
        const dx = (e.clientX - cx) / r.width;
        const dy = (e.clientY - cy) / r.height;
        el.style.transform = `rotateX(${(-dy * dampen).toFixed(2)}deg) rotateY(${(dx * dampen).toFixed(2)}deg)`;
      });
      el.addEventListener('mouseleave', () => {
        el.style.transform = 'rotateX(0deg) rotateY(0deg)';
      });
    });
  })();
</script>
</body></html>