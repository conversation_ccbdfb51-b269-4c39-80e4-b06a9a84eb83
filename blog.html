<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Technical Blog & Insights - <PERSON></title>
    <meta name="description" content="<PERSON> <PERSON>'s technical blog featuring insights on MERN stack development, AI automation, and modern web technologies." />
    <link rel="stylesheet" href="main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="icon" href="images/Logo.png" type="image/x-icon" />
    <style>
      .load-hidden { opacity: 0; transform: translateY(10px) scale(0.98); transition: opacity .6s ease, transform .6s ease; }
      .load-reveal { opacity: 1; transform: translateY(0) scale(1); transition: opacity .6s ease, transform .6s ease; }
    </style>
</head>
<body class="bg-background text-text-primary">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <nav class="container-max section-padding py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">TV</span>
                    </div>
                    <span class="text-xl font-semibold">Thomas van Rossum</span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="blog.html" class="text-accent font-medium" aria-current="page">Blog</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-surface transition-smooth" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Menu -->
            <div class="md:hidden mt-4 pb-4 border-t border-border pt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="blog.html" class="text-accent font-medium" aria-current="page">Blog</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-background via-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-6xl font-bold mb-6" data-load="0">
                    Technical <span class="text-gradient">Blog & Insights</span>
                </h1>
                <p class="text-xl text-text-secondary mb-8 leading-relaxed" data-load="140">
                    Dive deep into the world of modern web development, AI automation, and cutting-edge technologies. 
                    Learn from real-world experiences and practical insights.
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-12" data-load="220">
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">50+ Articles</span>
                    </div>
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">10K+ Readers</span>
                    </div>
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">Weekly Updates</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Article -->
    <section class="section-padding">
        <div class="container-max">
            <div class="mb-12">
                <h2 class="text-3xl font-bold mb-8 text-center">Featured Article</h2>
                <div class="card max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <img src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="Modern Web Development" class="w-full h-64 object-cover rounded-lg" />
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2 text-sm text-text-secondary">
                                <span>December 15, 2024</span>
                                <span>•</span>
                                <span>8 min read</span>
                                <span class="bg-accent/20 text-accent px-2 py-1 rounded text-xs">Featured</span>
                            </div>
                            <h3 class="text-2xl font-bold">Building Scalable MERN Applications: A Complete Guide</h3>
                            <p class="text-text-secondary">
                                Learn how to architect and build production-ready MERN stack applications that can scale to millions of users. 
                                This comprehensive guide covers everything from database design to deployment strategies.
                            </p>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React</span>
                                <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                                <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                                <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Express</span>
                            </div>
                            <a href="#" class="btn-primary inline-block">Read Full Article</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Categories -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Browse by Category</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="card text-center hover:shadow-hover transition-smooth cursor-pointer">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Web Development</h3>
                    <p class="text-text-secondary text-sm mb-4">Modern frameworks, best practices, and performance optimization</p>
                    <span class="text-accent text-sm font-medium">25 Articles</span>
                </div>

                <div class="card text-center hover:shadow-hover transition-smooth cursor-pointer">
                    <div class="w-16 h-16 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">AI & Automation</h3>
                    <p class="text-text-secondary text-sm mb-4">Machine learning, chatbots, and workflow automation</p>
                    <span class="text-accent text-sm font-medium">15 Articles</span>
                </div>

                <div class="card text-center hover:shadow-hover transition-smooth cursor-pointer">
                    <div class="w-16 h-16 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Database & Backend</h3>
                    <p class="text-text-secondary text-sm mb-4">Database design, API development, and server architecture</p>
                    <span class="text-accent text-sm font-medium">12 Articles</span>
                </div>

                <div class="card text-center hover:shadow-hover transition-smooth cursor-pointer">
                    <div class="w-16 h-16 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Mobile Development</h3>
                    <p class="text-text-secondary text-sm mb-4">React Native, Flutter, and cross-platform solutions</p>
                    <span class="text-accent text-sm font-medium">8 Articles</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Articles -->
    <section class="section-padding">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Recent Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Article 1 -->
                <article class="card hover:shadow-hover transition-smooth">
                    <img src="https://images.unsplash.com/photo-1633356122544-f134324a6cee?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="React Performance" class="w-full h-48 object-cover rounded-lg mb-4" />
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-sm text-text-secondary">
                            <span>December 10, 2024</span>
                            <span>•</span>
                            <span>5 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold">React Performance Optimization: 10 Essential Techniques</h3>
                        <p class="text-text-secondary text-sm">
                            Discover proven strategies to boost your React application's performance and deliver lightning-fast user experiences.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Performance</span>
                        </div>
                        <a href="#" class="text-accent hover:text-blue-400 transition-smooth text-sm font-medium">Read More →</a>
                    </div>
                </article>

                <!-- Article 2 -->
                <article class="card hover:shadow-hover transition-smooth">
                    <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="AI Automation" class="w-full h-48 object-cover rounded-lg mb-4" />
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-sm text-text-secondary">
                            <span>December 5, 2024</span>
                            <span>•</span>
                            <span>7 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold">Building AI-Powered Chatbots with OpenAI and Node.js</h3>
                        <p class="text-text-secondary text-sm">
                            Step-by-step guide to creating intelligent chatbots that can handle complex customer interactions.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">OpenAI</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                        </div>
                        <a href="#" class="text-accent hover:text-blue-400 transition-smooth text-sm font-medium">Read More →</a>
                    </div>
                </article>

                <!-- Article 3 -->
                <article class="card hover:shadow-hover transition-smooth">
                    <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="Database Design" class="w-full h-48 object-cover rounded-lg mb-4" />
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-sm text-text-secondary">
                            <span>November 28, 2024</span>
                            <span>•</span>
                            <span>6 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold">MongoDB vs PostgreSQL: Choosing the Right Database</h3>
                        <p class="text-text-secondary text-sm">
                            A comprehensive comparison to help you make the right database choice for your next project.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">PostgreSQL</span>
                        </div>
                        <a href="#" class="text-accent hover:text-blue-400 transition-smooth text-sm font-medium">Read More →</a>
                    </div>
                </article>
            </div>

            <div class="text-center mt-12">
                <a href="#" class="btn-primary">View All Articles</a>
            </div>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="section-padding bg-gradient-to-br from-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-2xl mx-auto">
                <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
                <p class="text-text-secondary mb-8">
                    Get the latest articles and insights delivered directly to your inbox. No spam, just valuable content.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent" />
                    <button class="btn-primary px-6 py-3">Subscribe</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary border-t border-border">
        <div class="container-max section-padding py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">TV</span>
                        </div>
                        <span class="text-lg font-semibold">Thomas van Rossum</span>
                    </div>
                    <p class="text-text-secondary mb-4">
                        MERN Stack Developer & AI Automation Specialist creating innovative solutions for modern businesses.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Navigation</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="index.html" class="hover:text-accent transition-smooth">Home</a></li>
                        <li><a href="projects.html" class="hover:text-accent transition-smooth">Projects</a></li>
                        <li><a href="about.html" class="hover:text-accent transition-smooth">About</a></li>
                        <li><a href="blog.html" class="hover:text-accent transition-smooth" aria-current="page">Blog</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Categories</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="#" class="hover:text-accent transition-smooth">Web Development</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">AI & Automation</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Database & Backend</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Mobile Development</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-border mt-8 pt-8 text-center text-text-secondary">
                <p>&copy; 2025 Thomas van Rossum. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        // Page-load stagger
        (function() {
          function run() {
            const loadEls = document.querySelectorAll('[data-load]');
            loadEls.forEach(el => el.classList.add('load-hidden'));
            loadEls.forEach(el => {
              const delay = parseInt(el.getAttribute('data-load') || '0', 10);
              setTimeout(() => el.classList.add('load-reveal'), delay);
            });
          }
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', run);
          } else {
            run();
          }
        })();
    </script>
</body>
</html>
