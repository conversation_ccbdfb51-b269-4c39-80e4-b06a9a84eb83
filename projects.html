<html><head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <title><PERSON> - <PERSON></title>
    <link href="images/Logo.png" rel="icon" type="image/x-icon"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
    <style type="text/tailwindcss">
        :root {
          --primary-color: #dce8f3;
          --background-color: #121416;
          --text-primary: #ffffff;
          --text-secondary: #a2abb3;
          --accent-color: #2c3135;
        }
        body {
          font-family: 'Space Grotesk', 'Noto Sans', sans-serif;
          background-color: var(--background-color);
          color: var(--text-primary);
        }
        .filter-active { @apply bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-bold; }
        .pre-animate { opacity: 0; transform: translateY(12px); }
        .reveal-in { opacity: 1; transform: translateY(0); transition: opacity .6s ease, transform .6s ease; }
        .load-hidden { opacity: 0; transform: translateY(10px) scale(0.98); transition: opacity .6s ease, transform .6s ease; }
        .load-reveal { opacity: 1; transform: translateY(0) scale(1); }
      </style>
    </head>
    <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[var(--background-color)] dark group/design-root overflow-x-hidden">
    <div class="layout-container flex h-full grow flex-col">
    <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-[var(--accent-color)] px-10 py-4 shadow-md">
    <div class="flex items-center gap-4 text-[var(--text-primary)]">
    <div class="size-6" aria-hidden="true">
    <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
    <path clip-rule="evenodd" d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z" fill="currentColor" fill-rule="evenodd"></path>
    </svg>
    </div>
    <h2 class="text-xl font-bold tracking-tight">Thomas van Rossum</h2>
    </div>
    <nav class="flex items-center gap-8">
    <a class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] text-base font-medium transition-colors" href="index.html#home">Home</a>
    <a class="text-[var(--text-primary)] text-base font-bold" href="projects.html" aria-current="page">Projects</a>
    <a class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] text-base font-medium transition-colors" href="index.html#about">About</a>
    <a class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] text-base font-medium transition-colors" href="blog.html">Blog</a>
    </nav>
    <a class="flex min-w-[100px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-11 px-6 bg-[var(--primary-color)] text-[var(--background-color)] text-base font-bold tracking-wide hover:bg-white transition-colors" href="mailto:<EMAIL>">
    <span class="truncate">Book a call</span>
    </a>
    </header>
    <main class="px-4 md:px-20 lg:px-40 flex flex-1 justify-center py-12">
    <div class="layout-content-container flex flex-col max-w-6xl flex-1">
    <div class="flex flex-col gap-6 items-center text-center mb-12">
    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-32 w-32 ring-4 ring-white/10" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuA-M1cygOIBW0eA3reNsFoqz2h3TjBe5ikVNIWSbsCQXNCObBUc2_RloeL2w2UZqzSzbteFpHtnmbb51KlUzlv8_1x3N6nScyXzprtBnY0-FtPfj9Zs7WjUT1ZJkAT26Irtnhd_mqbnrqeAbYrCnhwxCLY_KsDghx-JK0gH946riTYH_6ODvOJUBISnSD6cvnBaXSVXuxtNRoAVxGSPFaY3yGjCi5GCNHv3r_Oajcn5GiXFK95gr8-wY3xBSjvtIcmWA_id0WcbNws");' data-load="120"></div>
    <div class="flex flex-col items-center gap-2">
    <h1 class="text-[var(--text-primary)] text-5xl font-bold tracking-tighter" data-load="0">Projects & Case Studies</h1>
    <p class="text-[var(--text-secondary)] text-lg max-w-3xl text-center" data-load="180">
    Explore a curated collection of my projects, showcasing my expertise in MERN stack development and AI automation. Each project highlights the challenges, solutions, and outcomes, providing insights into my approach and capabilities.
  </p>
  <p class="text-orange-500 text-base mt-2 font-medium" data-load="260">
    Click on projects to see details
  </p>
    </div>
    </div>
    <div class="flex gap-4 p-3 justify-center mb-8 flex-wrap items-center">
    <div class="flex gap-4 items-center" id="filters">
    <button data-filter="all" class="h-10 shrink-0 flex items-center justify-center gap-x-2 rounded-lg bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-5 text-sm font-bold shadow-md hover:brightness-110 transition-colors filter-active">
        All Projects
      </button>
      <button data-filter="mern" class="h-10 shrink-0 flex items-center justify-center gap-x-2 rounded-lg bg-[var(--accent-color)] text-white px-5 text-sm font-medium shadow-sm hover:brightness-110 transition-colors">
        MERN Stack
      </button>
      <button data-filter="ai" class="h-10 shrink-0 flex items-center justify-center gap-x-2 rounded-lg bg-[var(--accent-color)] text-white px-5 text-sm font-medium shadow-sm hover:brightness-110 transition-colors">
        AI Automation
      </button>
      <button data-filter="saas" class="h-10 shrink-0 flex items-center justify-center gap-x-2 rounded-lg bg-[var(--accent-color)] text-white px-5 text-sm font-medium shadow-sm hover:brightness-110 transition-colors">
        SaaS
      </button>      
    </div>
    <div class="relative ml-4">
    <span class="material-symbols-outlined absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-secondary)]">search</span>
    <input id="search" class="h-10 w-64 rounded-lg border border-[var(--accent-color)] bg-[var(--accent-color)] bg-opacity-40 pl-10 pr-4 text-sm text-[var(--text-primary)] placeholder-[var(--text-secondary)] focus:border-[var(--primary-color)] focus:outline-none focus:ring-1 focus:ring-[var(--primary-color)] transition-colors" placeholder="Search projects..." type="text"/>
    </div>
    </div>
    <div id="grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
  <a href="hubspot-contact-sync.html" class="block pre-animate" data-tags="ai automation n8n hubspot" data-tilt>
    <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:border-orange-400 cursor-pointer">
      <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB9MWOdH__YfWyDhNN7FC8hXJlSCZ7eXohnzF5ot32cUu5VSjiEciNK3lmQEdrF2YJMR54IZWy2hZ1G6v2Z_XRhnj2oqKlSnGYu5eFRSQvAp-PYgnElRVuBENgXQMD_kW68tSTy9JlSow1jANjrKGYFDFg6PC6NCLXt0udkW73XuZh2XPhu1nkA6-zr1NY3j6tBe_Kkk7khBW3Wwm4F2v-X3pFVKf_ex9LKoD5llktcEP7ZRvVr6Zbs2hTUUmLL0pmUqbY2iTHr7Vs");'></div>
      <div class="flex flex-col gap-2">
        <h3 class="text-white text-xl font-bold">HubSpot Contact Sync</h3>
        <p class="text-[var(--text-secondary)] text-sm">An automated workflow solution that seamlessly synchronizes contact data between HubSpot and external systems, eliminating manual data entry and ensuring data consistency across platforms.</p>
        <p class="text-[var(--text-secondary)] text-xs font-mono">n8n, JavaScript, HubSpot API</p>
      </div>
    </div>
  </a>
<a href="Notion-productivity-tool.html" class="block pre-animate" data-tags="ai automation notion productivity" data-tilt>
  <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:border-orange-400 cursor-pointer">
    <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAeEgmM-tpjgj6Gg_bJ9Vy_6FzTShAOfVXVZdlYO64w4AXtVF59tgT14yGQrYUDDsUgqHQd8pCjnpakVvSjMx6eBESTX3edRCcjoWcM4guJfDD0VzrFdra0emffJrDj3OQ2Rjyw146CENkqVMDAdoDY9_DpvQvfCp9GJBeOa_sPDH7M8dh45O8vy5ywXKmq2rEG-7o8aoJxFeqbaPclPx6CEyaxH4eWTLaYuVfzW7FdA7OhsqlfVbgs9CFetE5LICgBACzDWcFzZtI");'></div>
    <div class="flex flex-col gap-2">
      <h3 class="text-white text-xl font-bold">Notion Productivity Suite</h3>
      <p class="text-[var(--text-secondary)] text-sm">A comprehensive productivity system built in Notion that streamlines project management, task tracking, and team collaboration with custom databases, automated workflows, and intelligent templates.</p>
      <p class="text-[var(--text-secondary)] text-xs font-mono">Notion API, JavaScript, Automation</p>
    </div>
  </div>
</a>
    <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-transform duration-300 hover:scale-105 hover:shadow-2xl pre-animate" data-tags="mern saas graphql apollo" data-tilt>
    <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDp1-PUUgrXiXk13EPWGtBb38317WWuIlhfCcPd5RIujhlRDm4H6rIFV3rFP0rQn4xDlMaNNwcsmntq9AFfGBvlunnGEwLEvjnEtKfW3b4gdaae_q-grRYsW2AZtVCro_Mh-0s53Sq-Zp-RgxEN2TM7LoOfZv2Jvv8xPGzCM00l3sNV5vdBizlsdwT21qf6xaSfOZJviMjvHF9jjHpgZveqp02Ywp7jKjx27QQKRdYX5u-CeiTTSpP1CWJNATx6wBmZF1cKMOP2iEw");'></div>
    <div class="flex flex-col gap-2">
    <h3 class="text-white text-xl font-bold">Project Management SaaS</h3>
    <p class="text-[var(--text-secondary)] text-sm">A SaaS application for project management, enabling effective team collaboration.</p>
    <p class="text-[var(--text-secondary)] text-xs font-mono">MERN, GraphQL, Apollo</p>
    </div>
    </div>
    <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-transform duration-300 hover:scale-105 hover:shadow-2xl pre-animate" data-tags="mobile fitness react-native firebase" data-tilt>
    <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCHJwFU0CIOuqrq96_ElkYKRbQIYjeGFzkCqyfaE4U9jncxQtdrbmY3UAgG0DTZocQalMfQVSmoZeUifkDZ1r1zxdsVsS7JJLRzfCW_rKBbovksu7On7soewmqw9TXPndPxGNWzrFuUF47DmAEZYypZv59W-n4Uf5R0qNtR63zHDGHyf4M3KJ-DNaonlOROgWJMzogPW6LAct7098AQruNcQoDFcwtg26B_pjYeg6KfRmIPPmWDyMpT-W9oJDsw8H2JdWOl_YJY13U");'></div>
    <div class="flex flex-col gap-2">
    <h3 class="text-white text-xl font-bold">Fitness Tracking App</h3>
    <p class="text-[var(--text-secondary)] text-sm">A mobile app for fitness tracking with workout logging and goal setting features.</p>
    <p class="text-[var(--text-secondary)] text-xs font-mono">React Native, Firebase</p>
    </div>
    </div>
    <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-transform duration-300 hover:scale-105 hover:shadow-2xl pre-animate" data-tags="mern data analysis dashboards charts" data-tilt>
    <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB7hq1PGSgCHvwptxG759MZud13Z7T79X44fCi9QnJJNZXjKLMdU56pEsdDhlLt3n1ZttbeW3GRtRKkhvnv0-99AX0YLECzHhRYn6S9thwTTX9Ct8M4Qs_795NgcNP8RzqFRV4ArubsXWDjJyU19LVFuD3jg99UeWyXRYLXCjXbFXj4vfcQXw-kIrZ2hobWX9ueHtuRTWAcMebnDk9FuOH0UoIa_nE00wy5UnyBqaHYlHR8xq33w1gcjus9q8t1Lc6NDN-HOD_Znt4");'></div>
    <div class="flex flex-col gap-2">
    <h3 class="text-white text-xl font-bold">Data Analysis Web App</h3>
    <p class="text-[var(--text-secondary)] text-sm">A MERN stack web app for data analysis with interactive dashboards.</p>
    <p class="text-[var(--text-secondary)] text-xs font-mono">MERN, Chart.js</p>
    </div>
    </div>
    <div class="flex flex-col gap-4 bg-[var(--accent-color)] bg-opacity-40 p-5 rounded-lg border border-[var(--accent-color)] transition-transform duration-300 hover:scale-105 hover:shadow-2xl pre-animate" data-tags="ai chatbot python rasa gcp" data-tilt>
    <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-md" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuC7t5XtlmvuOVAC1jnfr3Fb3oP6H4rsYzlSrivECqjLMVgN8X1vz2UBrgxNEtidlPz_S4GqxzE0HYXSTf4tgOUKV_Pb7T1ooyKsarUSteWXYovezr3ZhqVjkq6C9ywWEQsY-hE1yyopfUSV9MMPEHgAH0-8cTJFMpOu4GV_RdFPtqeEcIWGzz19PtVIIl_vwrUXGzXdb31oBawwvE0qgaT6UAfXvsN8ufG0yi5JJ7rGY_qYyqRPFe-MLW2amMxdTmxl0pe5Gcb6Yzs");'></div>
    <div class="flex flex-col gap-2">
    <h3 class="text-white text-xl font-bold">AI-Driven Chatbot</h3>
    <p class="text-[var(--text-secondary)] text-sm">An AI-driven chatbot for customer support, providing instant responses.</p>
    <p class="text-[var(--text-secondary)] text-xs font-mono">Python, Rasa, GCP</p>
    </div>
    </div>
    </div>
    <div class="flex items-center justify-center p-8 mt-8 gap-2">
    <a class="flex size-10 items-center justify-center rounded-lg hover:bg-[var(--accent-color)] transition-colors" href="#">
    <span class="material-symbols-outlined text-white">chevron_left</span>
    </a>
    <a class="text-sm font-bold flex size-10 items-center justify-center text-[var(--background-color)] rounded-lg bg-[var(--primary-color)]" href="#">1</a>
    <a class="text-sm font-medium flex size-10 items-center justify-center text-white rounded-lg hover:bg-[var(--accent-color)] transition-colors" href="#">2</a>
    <a class="text-sm font-medium flex size-10 items-center justify-center text-white rounded-lg hover:bg-[var(--accent-color)] transition-colors" href="#">3</a>
    <a class="flex size-10 items-center justify-center rounded-lg hover:bg-[var(--accent-color)] transition-colors" href="#">
    <span class="material-symbols-outlined text-white">chevron_right</span>
    </a>
    </div>
    </div>
    </main>
    </div>
    </div>
    <script>
      const filterButtons = Array.from(document.querySelectorAll('#filters button'));
      const searchInput = document.getElementById('search');
      const cards = Array.from(document.querySelectorAll('#grid > *'));

      function applyFilters() {
        const active = filterButtons.find(b => b.classList.contains('filter-active'))?.dataset.filter || 'all';
        const q = (searchInput.value || '').toLowerCase().trim();

        cards.forEach(card => {
          const tags = (card.getAttribute('data-tags') || '').toLowerCase();
          const text = card.textContent.toLowerCase();
          const matchesCategory = active === 'all' || tags.includes(active);
          const matchesSearch = !q || tags.includes(q) || text.includes(q);
          card.style.display = matchesCategory && matchesSearch ? '' : 'none';
        });
      }

      filterButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          filterButtons.forEach(b => b.classList.remove('filter-active'));
          btn.classList.add('filter-active');
          applyFilters();
        });
      });

      searchInput.addEventListener('input', applyFilters);
    </script>
    <script>
      // Reveal-on-scroll for project cards
      (function() {
        const items = document.querySelectorAll('#grid > *');
        items.forEach(el => el.classList.add('pre-animate'));
        const io = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('reveal-in');
              io.unobserve(entry.target);
            }
          });
        }, { threshold: 0.2 });
        items.forEach(el => io.observe(el));
      })();

      // Page-load stagger for hero
      (function() {
        const loadEls = document.querySelectorAll('[data-load]');
        loadEls.forEach(el => el.classList.add('load-hidden'));
        window.addEventListener('DOMContentLoaded', () => {
          loadEls.forEach(el => {
            const delay = parseInt(el.getAttribute('data-load') || '0', 10);
            setTimeout(() => el.classList.add('load-reveal'), delay);
          });
        });
      })();

      // Lightweight tilt for cards
      (function() {
        const tiltEls = document.querySelectorAll('[data-tilt]');
        const dampen = 12;
        tiltEls.forEach(el => {
          el.addEventListener('mousemove', (e) => {
            const r = el.getBoundingClientRect();
            const cx = r.left + r.width / 2;
            const cy = r.top + r.height / 2;
            const dx = (e.clientX - cx) / r.width;
            const dy = (e.clientY - cy) / r.height;
            el.style.transform = `perspective(800px) rotateX(${(-dy * dampen).toFixed(2)}deg) rotateY(${(dx * dampen).toFixed(2)}deg)`;
          });
          el.addEventListener('mouseleave', () => {
            el.style.transform = 'perspective(800px) rotateX(0deg) rotateY(0deg)';
          });
        });
      })();
    </script>
    </body></html>