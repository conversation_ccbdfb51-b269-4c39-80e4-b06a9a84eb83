<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>HubSpot Contact Sync - <PERSON></title>
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&family=Noto+Sans:wght@400;500;700;900&display=swap" rel="stylesheet" />
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  <link rel="icon" href="images/Logo.png" type="image/x-icon" />
  <style type="text/tailwindcss">
    :root {
      --primary-color: #0c7ff2;
      --background-color: #111418;
      --card-color: #1a2026;
      --text-primary: #ffffff;
      --text-secondary: #9cabba;
      --border-color: #283039;
    }
    body {
      font-family: "Space Grotesk", "Noto Sans", sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
    }
    .primary-button { @apply inline-flex items-center justify-center rounded-md px-6 py-2 font-semibold shadow-md transition bg-gradient-to-r from-orange-500 to-yellow-500 text-white hover:brightness-110; }
  </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)] min-h-screen">
  <a id="top"></a>
  <div class="max-w-3xl mx-auto py-10 px-4">
    <div class="bg-[var(--card-color)] rounded-xl shadow-lg p-6 flex flex-col gap-4">
      <a href="projects.html" class="primary-button mb-2">&larr; Back to Projects</a>
      <h1 class="text-4xl font-bold mb-1">HubSpot Contact Sync & Email Extraction</h1>
      <div>
        <h2 class="text-lg font-semibold mb-1">Tech Stack</h2>
        <ul class="flex flex-wrap gap-2 mb-2">
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">n8n</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">JavaScript</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">HubSpot API</li>
        </ul>
      </div>
      <div>
        <h2 class="text-lg font-semibold mb-1">Demo Video</h2>
        <div class="aspect-video w-full rounded-lg overflow-hidden bg-black mb-2">
          <video controls poster="images/project-hero.png" class="w-full h-full">
            <source src="videos/project-demo.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        I connected a HubSpot demo environment to my own backend and pulled live contact data using HubSpot’s API.
        I then parsed deeply nested fields such as identities and properties to extract useful information like name, email, and company.
      </p>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        <strong>What I did:</strong><br />
        – Used HubSpot’s contact API and access token authentication<br />
        – Wrote logic to handle nested contact structures and extract primary emails<br />
        – Converted raw data into clean JSON for further automation use
      </p>
      <p class="text-base text-[var(--text-secondary)] mb-2">
        This setup showcases my ability to read complex API responses, cleanly format data, and integrate CRM data into external systems for your business.
      </p>
      <a href="#top" class="primary-button mt-4">Back to Top &uarr;</a>
    </div>
  </div>
</body>
</html>
