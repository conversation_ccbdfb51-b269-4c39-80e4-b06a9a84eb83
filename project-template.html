<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Project Title - <PERSON></title>
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;700&family=Noto+Sans:wght@400;500;700;900&display=swap" rel="stylesheet" />
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  <style type="text/tailwindcss">
    :root {
      --primary-color: #0c7ff2;
      --background-color: #111418;
      --card-color: #1a2026;
      --text-primary: #ffffff;
      --text-secondary: #9cabba;
      --border-color: #283039;
    }
    body {
      font-family: "Space Grotesk", "Noto Sans", sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
    }
  </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)] min-h-screen">
  <a id="top"></a>
  <div class="max-w-3xl mx-auto py-10 px-4">
    <div class="bg-[var(--card-color)] rounded-xl shadow-lg p-6 flex flex-col gap-4">
      <a href="projects.html" class="inline-block mb-2 primary-button bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition px-6 py-2">&larr; Back to Projects</a>
      <h1 class="text-4xl font-bold mb-1">Project Title</h1>
      <div>
        <h2 class="text-lg font-semibold mb-1">Tech Stack</h2>
        <ul class="flex flex-wrap gap-2 mb-2">
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">React</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">Node.js</li>
          <li class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-3 py-1 rounded text-xs font-bold">MongoDB</li>
        </ul>
      </div>
      <div>
        <h2 class="text-lg font-semibold mb-1">Demo Video</h2>
        <div class="aspect-video w-full rounded-lg overflow-hidden bg-black mb-2">
          <video controls poster="images/project-hero.png" class="w-full h-full">
            <source src="videos/project-demo.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
      <p class="text-base text-[var(--text-secondary)] mb-2">This is a detailed description of the project. Explain the problem, your solution, and the impact. You can write as much as you want here to give visitors a deep understanding of your work.</p>
      <a href="#top" class="inline-block mt-4 primary-button bg-gradient-to-r from-orange-500 to-yellow-500 text-white font-semibold rounded-md shadow-md hover:brightness-110 transition px-6 py-2">Back to Top &uarr;</a>
    </div>
  </div>
</body>
</html> 